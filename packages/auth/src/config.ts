import { betterAuth, BetterAuthOptions } from 'better-auth';
import { nextCookies } from 'better-auth/next-js';
import { admin, organization } from 'better-auth/plugins';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { drizzle } from 'drizzle-orm/postgres-js';
import { eq, and } from 'drizzle-orm';
import postgres from 'postgres';
import * as schema from './schema';
import { ac, adminRole, trainerRole, customerRole } from './ac';

// Database connection
const connectionString = process.env.DATABASE_URL || 'postgresql://localhost:5432/loolookids';
const sql = postgres(connectionString);
const db = drizzle(sql, { schema });

const options = {
  database: drizzleAdapter(db, {
    provider: 'pg',
    usePlural: true,
  }),
  emailAndPassword: {
    enabled: true,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60, // Cache duration in seconds
    },
  },
  user: {
    additionalFields: {
      role: {
        type: 'string',
        defaultValue: 'admin',
      },
    },
  },
  plugins: [
    admin({
      ac,
      roles: {
        admin: adminRole,
        trainer: trainerRole,
        customer: customerRole,
      },
      defaultRole: 'customer',
      adminRoles: ['admin'],
    }),
    organization({
      creatorRole: 'owner',
      slugGenerator: (name: string) =>
        name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, ''),
      async sendInvitationEmail(data) {
        console.debug('🚨 TODO!! 🚨');

        // const inviteLink = `https://example.com/accept-invitation/${data.id}`;

        // sendOrganizationInvitation({
        //   email: data.email,
        //   invitedByUsername: data.inviter.user.name,
        //   invitedByEmail: data.inviter.user.email,
        //   teamName: data.organization.name,
        //   inviteLink,
        // });
      },
    }),
    nextCookies(),
  ],
  databaseHooks: {
    session: {
      create: {
        // find the first organization the user is a member of and set it as the active organization
        before: async (context) => {
          console.log('🐝 create session');

          try {
            const organizations = await db
              .select({
                id: schema.organizations.id,
                name: schema.organizations.name,
              })
              .from(schema.organizations)
              .innerJoin(schema.members, eq(schema.members.organizationId, schema.organizations.id))
              .where(and(eq(schema.members.userId, context.userId)));

            // we should only have one or none
            // one if onboarded
            // none if not onboarded

            return {
              data: {
                ...context,
                activeOrganizationId: organizations?.[0]?.id,
              },
            };
          } catch (error) {
            console.log('❌ Error listing organizations:', error);

            return {
              data: context,
            };
          }
        },
      },
    },
  },
} satisfies BetterAuthOptions;

export const auth: ReturnType<typeof betterAuth<typeof options>> = betterAuth(options);

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session.user;

export { db };
