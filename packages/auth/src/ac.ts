import { createAccessControl } from 'better-auth/plugins/access';

const statement = {
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['create', 'read', 'update', 'delete'],
  financial: ['create', 'read', 'update', 'delete'],
  organization: ['create', 'read', 'update'],
} as const;

const ac = createAccessControl(statement);

const adminRole = ac.newRole({
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['create', 'read', 'update', 'delete'],
  financial: ['create', 'read', 'update', 'delete'],
  organization: ['create', 'read', 'update'],
});

const trainerRole = ac.newRole({
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['read'],
  organization: ['read'],
});

const customerRole = ac.newRole({
  workout: ['read'], // should be able to create, read, update for themselves
  customer: ['read'], // should be able to create, read, update themselves
  organization: ['read'],
});

export { adminRole, trainerRole, ac };
