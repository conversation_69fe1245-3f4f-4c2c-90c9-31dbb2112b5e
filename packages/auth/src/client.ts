'use client';

import { organizationClient, adminClient } from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import { ac, adminRole, trainerRole } from './ac';

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_BETTER_AUTH_URL || 'http://localhost:3000',
  plugins: [
    organizationClient(),
    adminClient({
      ac,
      roles: {
        admin: adminRole,
        trainer: trainerRole,
      },
    }),
  ],
});

export const { signIn, signOut, signUp, useSession, $Infer } = authClient;

export type Session = typeof $Infer.Session;
export type User = typeof $Infer.Session.user;
