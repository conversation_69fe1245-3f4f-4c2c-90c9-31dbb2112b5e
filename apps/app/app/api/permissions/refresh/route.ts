import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse } from '@/lib/api-utils';
import { getAllCommonPermissionsCached } from '@/lib/permission-utils';

// POST /api/permissions/refresh - Manually refresh current user's permissions
export const POST = withAuth(async (request: NextRequest, userSession) => {
  try {
    // Refresh permissions for the current user
    await getAllCommonPermissionsCached(userSession.user.id);
    
    return createSuccessResponse({ 
      message: 'Permissions refreshed successfully',
      userId: userSession.user.id 
    });
  } catch (error) {
    console.error('Failed to refresh permissions:', error);
    return createErrorResponse('Internal Server Error', 'Failed to refresh permissions', 500);
  }
}); 