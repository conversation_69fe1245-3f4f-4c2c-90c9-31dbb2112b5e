import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse } from '@/lib/api-utils';
import { getCachedUserRole } from '@/lib/permission-utils';

// POST /api/permissions/refresh - Manually refresh current user's role
export const POST = withAuth(async (request: NextRequest, userSession) => {
  try {
    // Refresh role for the current user
    await getCachedUserRole(userSession.user.id);

    return createSuccessResponse({
      message: 'Role refreshed successfully',
      userId: userSession.user.id
    });
  } catch (error) {
    console.error('Failed to refresh permissions:', error);
    return createErrorResponse('Internal Server Error', 'Failed to refresh permissions', 500);
  }
}); 