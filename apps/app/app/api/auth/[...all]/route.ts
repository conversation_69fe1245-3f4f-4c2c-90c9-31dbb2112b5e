import { auth, toNextJsHand<PERSON> } from '@workspace/auth/server';
import { getAllCommonPermissionsCached } from '@/lib/permission-utils';

// Create the auth handler
const { GET, POST } = toNextJsHandler(auth.handler);

// Export the handlers
export { GET, POST };

// Note: Permission cache will be initialized when the user first accesses a protected route
// or we can add it to specific auth events if needed
