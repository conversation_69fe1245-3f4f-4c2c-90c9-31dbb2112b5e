import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse } from '@/lib/api-utils';
import { getCachedUserRole } from '@/lib/permission-utils';

// GET /api/user/role - Get current user's role
export const GET = withAuth(async (request: NextRequest, userSession) => {
  try {
    const role = await getCachedUserRole(userSession.user.id);
    
    return createSuccessResponse({ 
      role,
      userId: userSession.user.id 
    });
  } catch (error) {
    console.error('Failed to get user role:', error);
    return createErrorResponse('Internal Server Error', 'Failed to get user role', 500);
  }
});
