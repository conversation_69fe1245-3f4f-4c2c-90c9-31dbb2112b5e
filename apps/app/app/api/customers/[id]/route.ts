import { NextRequest } from 'next/server';
import { db, customers, users, type User } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { updateCustomerSchema, type CustomerResponse } from '@/lib/validations';
import {
  withAuth,
  withTrainer<PERSON><PERSON>,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { calculateCustomerTotalSessions } from '@/lib/package-service';

// GET /api/customers/[id] - Get single customer
export const GET = withTrainerAuth(
  { customer: ['read'] },
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const trainerId = await getTrainerIdFromUser(userSession);

      if (!trainerId) {
        return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
      }

      const [customer] = await db
        .select({
          id: customers.id,
          trainerId: customers.trainerId,
          parentName: customers.parentName,
          userId: customers.userId,
          user_name: users.name,
          user_email: users.email,
          user_phone: users.phone,
          user_createdAt: users.createdAt,
          user_updatedAt: users.updatedAt,
        })
        .from(customers)
        .innerJoin(users, eq(customers.userId, users.id))
        .where(and(eq(customers.id, id), eq(customers.trainerId, trainerId)));

      if (!customer) {
        return createErrorResponse('Not Found', 'Customer not found', 404);
      }

      // Calculate total sessions from packages
      const totalSessions = await calculateCustomerTotalSessions(customer.id);

      const response: CustomerResponse = {
        id: customer.id,
        trainerId: customer.trainerId,
        name: customer.user_name,
        email: customer.user_email,
        phone: customer.user_phone,
        totalSessions,
        parentName: customer.parentName,
        createdAt: customer.user_createdAt,
        updatedAt: customer.user_updatedAt,
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// PUT /api/customers/[id] - Update customer
export const PUT = withTrainerAuth(
  { customer: ['update'] },
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const trainerId = await getTrainerIdFromUser(userSession);

      if (!trainerId) {
        return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
      }

      const bodyValidation = await validateRequestBody(request, updateCustomerSchema);
      if (!bodyValidation.success) {
        return createErrorResponse(
          bodyValidation.error.error,
          bodyValidation.error.message,
          400,
          bodyValidation.error.details
        );
      }

      const updateData = bodyValidation.data;

      // Check if customer exists and belongs to trainer
      const [existingCustomer] = await db
        .select()
        .from(customers)
        .where(and(eq(customers.id, id), eq(customers.trainerId, trainerId)));

      if (!existingCustomer) {
        return createErrorResponse('Not Found', 'Customer not found', 404);
      }

      // Update user fields
      const userUpdate: Partial<User> = { name: updateData.name, updatedAt: new Date() };
      if (typeof updateData.email !== 'undefined') {
        userUpdate.email = updateData.email;
      }

      if (typeof updateData.phone !== 'undefined') {
        userUpdate.phone = updateData.phone;
      }

      await db.update(users).set(userUpdate).where(eq(users.id, existingCustomer.userId));

      // Update customer-specific fields
      const [updatedCustomer] = await db
        .update(customers)
        .set({
          parentName: updateData.parentName,
        })
        .where(eq(customers.id, id))
        .returning();

      if (!updatedCustomer) {
        throw new Error('Failed to update customer');
      }

      // Get updated user info
      const [updatedUser] = await db.select().from(users).where(eq(users.id, existingCustomer.userId));
      const userInfo = updatedUser
        ? updatedUser
        : { name: '', email: '', phone: '', createdAt: new Date(), updatedAt: new Date() };

      // Calculate total sessions from packages
      const totalSessions = await calculateCustomerTotalSessions(updatedCustomer.id);

      const response: CustomerResponse = {
        id: updatedCustomer.id,
        trainerId: updatedCustomer.trainerId,
        name: userInfo.name,
        email: userInfo.email,
        phone: userInfo.phone,
        totalSessions,
        parentName: updatedCustomer.parentName,
        createdAt: userInfo.createdAt,
        updatedAt: userInfo.updatedAt,
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// DELETE /api/customers/[id] - Delete customer
export const DELETE = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Check if customer exists and belongs to trainer
    const [existingCustomer] = await db
      .select()
      .from(customers)
      .where(and(eq(customers.id, id), eq(customers.trainerId, trainerId)));

    if (!existingCustomer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    // Delete customer
    await db.delete(customers).where(eq(customers.id, id));

    return createSuccessResponse({ message: 'Customer deleted successfully' });
  } catch (error) {
    return handleApiError(error);
  }
});
