import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse, validateRequestBody } from '@/lib/api-utils';
import { auth, db, trainers, users } from '@workspace/auth/server';
import { eq } from 'drizzle-orm';
import { z } from 'zod';
import { headers } from 'next/headers';
import { refreshUserPermissions } from '@/lib/permission-utils.server';

const onboardingSchema = z.object({
  organizationName: z.string().min(1, 'Organization name is required'),
  organizationDescription: z.string().optional(),
  phone: z.string().optional(),
});

// POST /api/trainer/onboarding - Trainer onboarding for owner
export const POST = withAuth(async (request: NextRequest, userSession) => {
  try {
    const bodyValidation = await validateRequestBody(request, onboardingSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const { organizationName, organizationDescription, phone } = bodyValidation.data;

    const user = userSession.user;

    // Check if trainer already exists
    const [existingTrainer] = await db.select({ id: trainers.id }).from(trainers).where(eq(trainers.userId, user.id));

    if (existingTrainer) {
      return createErrorResponse('Conflict', 'Trainer profile already exists', 409);
    }

    const [newTrainer, organizationId] = await db.transaction(async (tx) => {
      await tx.update(users).set({ phone }).where(eq(users.id, user.id));
      await tx.insert(trainers).values({ userId: user.id });

      // Create trainer profile
      const [newTrainer] = await tx
        .insert(trainers)
        .values({
          userId: user.id,
        })
        .returning();

      if (!newTrainer) {
        throw new Error('Failed to create trainer profile');
      }

      // Update user data
      await tx.update(users).set({ phone }).where(eq(users.id, user.id));

      // Create organization for new owners that just registered
      const organization = await auth.api.createOrganization({
        body: {
          userId: user.id,
          name: organizationName,
          slug: organizationName
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, ''),
          metadata: organizationDescription ? { description: organizationDescription } : undefined,
        },
        // This endpoint requires session cookies.
        headers: await headers(),
      });

      if (!organization) {
        throw new Error('Failed to create organization');
      }

      return [newTrainer, organization.id];
    });

    // Refresh permissions after organization creation and role assignment
    await refreshUserPermissions(user.id);

    return createSuccessResponse(
      {
        trainer: newTrainer,
        organizationId,
      },
      201
    );
  } catch (error) {
    console.error('❌ Failed to complete trainer onboarding', error);
    return createErrorResponse('Internal Server Error', 'Failed to complete onboarding', 500);
  }
});
