import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse } from '@/lib/api-utils';
import { db, members, organizations, trainers, users } from '@workspace/auth/server';
import { eq } from 'drizzle-orm';

// GET /api/trainer/profile - Check if trainer profile exists
export const GET = withAuth(async (request: NextRequest, userSession) => {
  try {
    const [trainer] = await db
      .select({
        id: trainers.id,
        name: users.name,
        email: users.email,
        phone: users.phone,
        organizationId: organizations.id,
      })
      .from(trainers)
      .innerJoin(users, eq(trainers.userId, users.id))
      .innerJoin(members, eq(members.userId, users.id))
      .innerJoin(organizations, eq(organizations.id, members.organizationId))
      .where(eq(trainers.userId, userSession.user.id));

    if (!trainer) {
      return createErrorResponse('Not Found', 'Trainer profile not found', 404);
    }

    console.debug('✅ /api/trainer/profile GET', trainer);

    return createSuccessResponse(trainer);
  } catch (error) {
    console.error('❌ /api/trainer/profile GET', error);
    return createErrorResponse('Internal Server Error', 'Failed to fetch trainer profile', 500);
  }
});
