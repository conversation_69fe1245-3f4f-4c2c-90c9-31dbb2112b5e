import { NextRequest } from 'next/server';
import { db, packages } from '@workspace/auth/server';
import { eq, and, desc, asc, count, ilike } from 'drizzle-orm';
import {
  createPackageSchema,
  packageQuerySchema,
  type PaginatedResponse,
  type PackageResponse,
} from '@/lib/validations';
import {
  withAuth,
  withAdminAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { createPackage } from '@/lib/package-service';

// GET /api/packages - Get all packages for trainer
export const GET = withAuth(async (request: NextRequest, userSession) => {
  try {
    const trainerId = await getTrainerIdFromUser(userSession);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const queryValidation = validateQueryParams(request, packageQuerySchema);
    if (!queryValidation.success) {
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    const { search, isActive, limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build conditions
    const conditions = [eq(packages.trainerId, trainerId)];

    if (search) {
      conditions.push(ilike(packages.name, `%${search}%`));
    }

    if (isActive !== undefined) {
      // Accept both string and boolean
      const isActiveBool = isActive === true || isActive === 'true';
      conditions.push(eq(packages.isActive, isActiveBool));
    }

    // Build order by
    const validSortBy = sortBy || 'name';
    const orderDirection = sortOrder === 'desc' ? desc : asc;

    const sortColumns = {
      name: packages.name,
      sessionCount: packages.sessionCount,
      price: packages.price,
      createdAt: packages.createdAt,
    };

    const orderByColumn = sortColumns[validSortBy as keyof typeof sortColumns] || packages.name;

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(packages)
      .where(and(...conditions));

    if (!totalResult) {
      throw new Error('Failed to fetch total count');
    }

    const total = totalResult.count;

    // Get packages
    const packagesList = await db
      .select()
      .from(packages)
      .where(and(...conditions))
      .orderBy(orderDirection(orderByColumn))
      .limit(limit || 20)
      .offset(offset || 0);

    const validLimit = limit || 20;
    const validOffset = offset || 0;

    const response: PaginatedResponse<PackageResponse> = {
      data: packagesList.map((pkg) => ({
        id: pkg.id,
        trainerId: pkg.trainerId,
        name: pkg.name,
        description: pkg.description,
        sessionCount: pkg.sessionCount,
        price: pkg.price,
        isActive: pkg.isActive,
        createdAt: pkg.createdAt,
        updatedAt: pkg.updatedAt,
      })),
      pagination: {
        total,
        limit: validLimit,
        offset: validOffset,
        hasMore: validOffset + validLimit < total,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// POST /api/packages - Create new package (Admin only)
export const POST = withAdminAuth({ package: ['create'] }, async (request: NextRequest, userSession) => {
  try {
    const trainerId = await getTrainerIdFromUser(userSession);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, createPackageSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const packageData = bodyValidation.data;

    // Ensure isActive is always a boolean, defaulting to true if undefined
    const packageDataWithDefaults = {
      ...packageData,
      isActive: packageData.isActive ?? true,
    };

    // Create package
    const newPackage = await createPackage(trainerId, packageDataWithDefaults);

    if (!newPackage) {
      throw new Error('Failed to create package');
    }

    const response: PackageResponse = {
      id: newPackage.id,
      trainerId: newPackage.trainerId,
      name: newPackage.name,
      description: newPackage.description,
      sessionCount: newPackage.sessionCount,
      price: newPackage.price,
      isActive: newPackage.isActive,
      createdAt: newPackage.createdAt,
      updatedAt: newPackage.updatedAt,
    };

    return createSuccessResponse(response, 201);
  } catch (error) {
    return handleApiError(error);
  }
});
