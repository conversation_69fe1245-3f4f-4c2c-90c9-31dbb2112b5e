import type { Metada<PERSON> } from 'next';

export const metadata: Metadata = {
  title: 'LooLooKids',
};

import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset } from '@/components/ui/sidebar';
import BigCalendar from '@/components/big-calendar';
import { CustomerSidebar } from '@/components/customers/customer-sidebar';
import { CalendarPageWrapper } from '@/components/calendar-page-wrapper';

export default function Page() {
  return (
    <>
      <CalendarPageWrapper>
        <AppSidebar />
        <SidebarInset>
          <div className="flex flex-1 flex-col gap-4 p-2 pt-0">
            <BigCalendar />
          </div>
        </SidebarInset>
        <CustomerSidebar />
      </CalendarPageWrapper>
    </>
  );
}
