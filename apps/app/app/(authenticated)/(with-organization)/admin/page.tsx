import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset } from '@/components/ui/sidebar';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { SessionManagementDashboard } from '@/components/admin/session-management-dashboard';
import { MemberManagement } from '@/components/admin/member-management';
import { PackageManagement } from '@/components/admin/package-management';

export const metadata = {
  title: 'Admin - LooLooFit',
  description: 'Session management and administrative tools',
};

export default async function AdminPage() {
  return (
    <>
      <AppSidebar />
      <SidebarInset>
        <div className="flex flex-1 flex-col gap-4 p-2 pt-0">
          <div className="py-5 px-4">
            <div className="mb-6">
              <h1 className="text-2xl font-bold">Admin Dashboard</h1>
              <p className="text-muted-foreground">Manage sessions, packages, and administrative settings</p>
            </div>
            <Tabs defaultValue="sessions" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                {hasFinancialPermission && <TabsTrigger value="sessions">Session Management</TabsTrigger>}
                {hasPackagePermission && <TabsTrigger value="packages">Package Management</TabsTrigger>}
                {hasOrganizationPermission && <TabsTrigger value="members">Members Management</TabsTrigger>}
              </TabsList>
              {hasFinancialPermission && (
                <TabsContent value="sessions" className="mt-6">
                  <SessionManagementDashboard />
                </TabsContent>
              )}
              {hasPackagePermission && (
                <TabsContent value="packages" className="mt-6">
                  <PackageManagement />
                </TabsContent>
              )}
              {hasOrganizationPermission && (
                <TabsContent value="members" className="mt-6">
                  <MemberManagement />
                </TabsContent>
              )}
            </Tabs>
          </div>
        </div>
      </SidebarInset>
    </>
  );
}
