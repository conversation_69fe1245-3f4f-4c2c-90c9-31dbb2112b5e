import { ReactNode } from 'react';
import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { CalendarProvider } from '@/components/event-calendar/calendar-context';
import { RedirectToSignIn } from '@workspace/auth';
import { canAccessFinancial, canReadPackages, canAccessOrganization } from '@/lib/permission-utils';
import { auth } from '@workspace/auth/server';
import { AdminPermissionProvider } from '@/components/admin/admin-permission-context';
import { SidebarProvider } from '@/components/ui/sidebar';
import { getAuthenticatedUser } from '@/lib/api-utils';

export default async function AuthenticatedLayout({ children }: { children: ReactNode }) {
  // Forward cookies for authentication
  const cookieStore = await cookies();

  // TODO: what is the cookie doing
  // Check onboarding status server-side
  const trainerResponse = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/trainer/profile`,
    {
      headers: { Cookie: cookieStore.toString() },
      cache: 'no-store',
    }
  );

  if (trainerResponse.status === 404) {
    // Not onboarded, redirect to onboarding
    redirect('/onboarding');
  }

  // Get user session for permission checking
  let hasFinancialPermission = false;
  let hasPackagePermission = false;
  let hasOrganizationPermission = false;

  const userSession = await getAuthenticatedUser(undefined, await headers());
  if (!userSession) {
    console.debug('❌ No user session');
    return <RedirectToSignIn />;
  }

  console.debug('❓ userSession', userSession?.session);
  if (!userSession?.session?.activeOrganizationId) {
    console.debug('❌ No active organization');

    // Still using old sessions before onboarding, need to refresh
    await auth.api.setActiveOrganization({
      body: { organizationId: (await trainerResponse.json()).organizationId },
    });

    redirect('/');
  }

  // Check role-based permissions
  hasFinancialPermission = await canAccessFinancial(userSession.user.id);
  hasPackagePermission = await canReadPackages(userSession.user.id);
  hasOrganizationPermission = await canAccessOrganization(userSession.user.id);

  return (
    <>
      <RedirectToSignIn />

      <AdminPermissionProvider
        value={{
          hasFinancialPermission,
          hasPackagePermission,
          hasOrganizationPermission,
        }}
      >
        <CalendarProvider>
          <SidebarProvider>{children}</SidebarProvider>
        </CalendarProvider>
      </AdminPermissionProvider>
    </>
  );
}
