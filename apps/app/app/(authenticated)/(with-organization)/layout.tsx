import { ReactNode } from 'react';
import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { CalendarProvider } from '@/components/event-calendar/calendar-context';
import { RedirectToSignIn } from '@workspace/auth';
import { getNowEpoch, getPermissionsForLayout, PERMISSION_COOKIE_NAME } from '@/lib/permission-utils';
import { auth } from '@workspace/auth/server';
import { AdminPermissionProvider } from '@/components/admin/admin-permission-context';
import { SidebarProvider } from '@/components/ui/sidebar';
import { getAuthenticatedUser } from '@/lib/api-utils';

export default async function AuthenticatedLayout({ children }: { children: ReactNode }) {
  // Forward cookies for authentication
  const cookieStore = await cookies();

  // TODO: what is the cookie doing
  // Check onboarding status server-side
  const trainerResponse = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/trainer/profile`,
    {
      headers: { Cookie: cookieStore.toString() },
      cache: 'no-store',
    }
  );

  if (trainerResponse.status === 404) {
    // Not onboarded, redirect to onboarding
    redirect('/onboarding');
  }

  // Forward cookies for authentication
  const permCookie = cookieStore.get(PERMISSION_COOKIE_NAME)?.value;
  let hasFinancialPermission = false;
  let hasPackagePermission = false;
  let hasOrganizationPermission = false;
  let shouldCheckPermissions = false;

  if (permCookie) {
    console.debug('🍪', permCookie);
    try {
      const parsed = JSON.parse(permCookie);
      console.debug('🍪', parsed);
      console.debug('🍪 _EXPIRED', !(parsed.expiresAt > getNowEpoch()));
      if (parsed.expiresAt > getNowEpoch()) {
        hasFinancialPermission = !!parsed.f;
        hasPackagePermission = !!parsed.p;
        hasOrganizationPermission = !!parsed.o;
      } else {
        shouldCheckPermissions = true;
      }
    } catch {
      shouldCheckPermissions = true;
    }
  } else {
    shouldCheckPermissions = true;
  }

  console.debug('❓ shouldCheckPermissions', shouldCheckPermissions);

  if (shouldCheckPermissions) {
    const userSession = await getAuthenticatedUser(undefined, await headers());
    if (!userSession) {
      console.debug('❌ shouldCheckPermissions - No user session');
      return <RedirectToSignIn />;
    }

    console.debug('❓ userSession', userSession?.session);
    if (!userSession?.session?.activeOrganizationId) {
      console.debug('❌ shouldCheckPermissions - No active organization');

      // Still using old sessions before onboarding, need to refresh
      await auth.api.setActiveOrganization({
        body: { organizationId: (await trainerResponse.json()).organizationId },
      });

      redirect('/');
    }

    const permissions = await getPermissionsForLayout(userSession.user.id);
    hasFinancialPermission = permissions.financial ?? false;
    hasPackagePermission = permissions.package ?? false;
    hasOrganizationPermission = permissions.organization ?? false;
  }

  return (
    <>
      <RedirectToSignIn />

      <AdminPermissionProvider
        value={{
          hasFinancialPermission,
          hasPackagePermission,
          hasOrganizationPermission,
        }}
      >
        <CalendarProvider>
          <SidebarProvider>{children}</SidebarProvider>
        </CalendarProvider>
      </AdminPermissionProvider>
    </>
  );
}
