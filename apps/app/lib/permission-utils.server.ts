'use server';

import { auth } from '@workspace/auth/server';
import { getAllCommonPermissionsCached } from './permission-utils';

/**
 * Server Action: Force refresh permissions for a user
 * Use this after role/organization changes
 */
export async function refreshUserPermissions(userId: string): Promise<void> {
  await getAllCommonPermissionsCached(userId);
}

/**
 * Server Action: Manual permission refresh for current user
 * Can be called from client components
 */
export async function refreshCurrentUserPermissions(): Promise<{ success: boolean; message?: string }> {
  try {
    const session = await auth.api.getSession({ headers: await import('next/headers').then((m) => m.headers()) });
    if (!session?.user?.id) {
      return { success: false, message: 'No authenticated user found' };
    }

    await getAllCommonPermissionsCached(session.user.id);
    return { success: true, message: 'Permissions refreshed successfully' };
  } catch (error) {
    console.error('Failed to refresh permissions:', error);
    return { success: false, message: 'Failed to refresh permissions' };
  }
}
