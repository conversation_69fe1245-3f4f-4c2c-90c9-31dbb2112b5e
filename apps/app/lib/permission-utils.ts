import { auth } from '@workspace/auth/server';
import { cookies } from 'next/headers';

export const PERMISSION_COOKIE_NAME = 'user_permissions_cache';
const CACHE_DURATION = 15 * 60 * 1000; // 15 minutes

interface PermissionCache {
  permissions: Record<string, boolean>;
  userId: string;
  expiresAt: number;
  lastUpdated: number;
}

export function getNowEpoch() {
  return Math.floor(Date.now() / 1000);
}

/**
 * For use in layouts/server components: Only reads the permission cookie.
 * If missing/expired, fetches fresh permissions but does NOT set the cookie.
 */
export async function getPermissionsForLayout(userId: string): Promise<Record<string, boolean>> {
  const cookieStore = await cookies();
  const cachedPermissions = cookieStore.get(PERMISSION_COOKIE_NAME);

  if (cachedPermissions) {
    try {
      const cached: PermissionCache = JSON.parse(cachedPermissions.value);
      if (cached.userId === userId && cached.expiresAt > Date.now()) {
        return cached.permissions;
      }
    } catch {
      // Ignore parse errors, fall through to fetch
    }
  }
  // Fallback: fetch fresh, but do NOT set cookie
  return await fetchAllCommonPermissions(userId);
}

/**
 * Get cached permissions for a user, or fetch and cache if missing/expired.
 * Only use in server actions or API routes (can set cookies).
 */
export async function getCachedUserPermissions(
  userId: string,
  fetchPermissions: () => Promise<Record<string, boolean>>
): Promise<Record<string, boolean>> {
  const cookieStore = await cookies();
  const cachedPermissions = cookieStore.get(PERMISSION_COOKIE_NAME);

  if (cachedPermissions) {
    console.debug('🐞 cachedPermissions found');

    try {
      const cached: PermissionCache = JSON.parse(cachedPermissions.value);
      if (cached.userId === userId && cached.expiresAt > Date.now()) {
        console.debug('🐞 cachedPermissions still valid');

        return cached.permissions;
      }
    } catch {
      // Ignore parse errors, fall through to fetch
    }
  }

  console.debug('🐞 cachedPermissions miss or expired');
  // Cache miss or expired: fetch fresh permissions and update cookie
  const permissions = await fetchPermissions();
  cookieStore.set(
    PERMISSION_COOKIE_NAME,
    JSON.stringify({
      permissions,
      userId,
      expiresAt: Date.now() + CACHE_DURATION,
      lastUpdated: Date.now(),
    }),
    {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: CACHE_DURATION / 1000,
    }
  );
  return permissions;
}

/**
 * Check a specific admin permission for a user (no cache, single check)
 */
export async function checkAdminPermission(userId: string, permissions: Record<string, string[]>): Promise<boolean> {
  try {
    const result = await auth.api.userHasPermission({
      body: {
        userId,
        permissions,
      },
    });
    if (result.error) {
      console.error('❌ Error checking admin permissions:', result.error);
      return false;
    }
    return result.success;
  } catch (error) {
    console.error('❌ Error checking admin permissions:', error);
    return false;
  }
}

/**
 * Helper to fetch all common permissions for a user (for caching)
 */
export async function fetchAllCommonPermissions(userId: string): Promise<Record<string, boolean>> {
  const checks: [string, Record<string, string[]>][] = [
    ['financial', { financial: ['create', 'read', 'update'] }],
    ['package', { package: ['create', 'read', 'update', 'delete'] }],
    ['organization', { organization: ['create', 'read', 'update'] }],
    ['admin', { admin: ['read'] }],
    ['customer', { customer: ['read'] }],
  ];
  const results: Record<string, boolean> = {};
  await Promise.all(
    checks.map(async ([key, perms]) => {
      results[key] = await checkAdminPermission(userId, perms);
    })
  );
  return results;
}

/**
 * Helper: get all common permissions for a user, using the cache (server action/route only)
 */
export async function getAllCommonPermissionsCached(userId: string): Promise<Record<string, boolean>> {
  return getCachedUserPermissions(userId, () => fetchAllCommonPermissions(userId));
}

/**
 * Helper: check if user can access admin (using cache)
 */
export async function canAccessAdminCached(userId: string): Promise<boolean> {
  const perms = await getAllCommonPermissionsCached(userId);
  return !!perms.admin;
}

/**
 * Helper: check if user can access organization (using cache)
 */
export async function canAccessOrganizationCached(userId: string): Promise<boolean> {
  const perms = await getAllCommonPermissionsCached(userId);
  return !!perms.organization;
}

/**
 * Helper: check if user can access customer (using cache)
 */
export async function canAccessCustomerCached(userId: string): Promise<boolean> {
  const perms = await getAllCommonPermissionsCached(userId);
  return !!perms.customer;
}

/**
 * Helper: pass permission to check (using cache)
 */
export async function checkPermissionCached(userId: string, permission: string): Promise<boolean> {
  const perms = await getAllCommonPermissionsCached(userId);
  return !!perms[permission];
}
