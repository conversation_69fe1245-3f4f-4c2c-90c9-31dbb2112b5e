import { auth } from '@workspace/auth/server';
import { cookies } from 'next/headers';

export const ROLE_CACHE_NAME = 'user_role_cache';
const CACHE_DURATION = 15 * 60 * 1000; // 15 minutes

export type UserRole = 'admin' | 'trainer' | 'customer';

interface RoleCache {
  role: UserRole | null;
  userId: string;
  expiresAt: number;
  lastUpdated: number;
}

export function getNowEpoch() {
  return Math.floor(Date.now() / 1000);
}

/**
 * For use in layouts/server components: Only reads the role cookie.
 * If missing/expired, fetches fresh role but does NOT set the cookie.
 */
export async function getUserRoleForLayout(userId: string): Promise<UserRole | null> {
  const cookieStore = await cookies();
  const cachedRole = cookieStore.get(ROLE_CACHE_NAME);

  if (cachedRole) {
    try {
      const cached: RoleCache = JSON.parse(cachedRole.value);
      if (cached.userId === userId && cached.expiresAt > Date.now()) {
        return cached.role;
      }
    } catch {
      // Ignore parse errors, fall through to fetch
    }
  }
  // Fallback: fetch fresh, but do NOT set cookie
  return await fetchUserRole(userId);
}

/**
 * Get cached permissions for a user, or fetch and cache if missing/expired.
 * Only use in server actions or API routes (can set cookies).
 */
export async function getCachedUserRole(userId: string): Promise<UserRole | null> {
  const cookieStore = await cookies();
  const cachedRole = cookieStore.get(ROLE_CACHE_NAME);

  if (cachedRole) {
    console.debug('🐞 cachedRole found');

    try {
      const cached: RoleCache = JSON.parse(cachedRole.value);
      if (cached.userId === userId && cached.expiresAt > Date.now()) {
        console.debug('🐞 cachedRole still valid');
        return cached.role;
      }
    } catch {
      // Ignore parse errors, fall through to fetch
    }
  }

  console.debug('🐞 cachedRole miss or expired');
  // Cache miss or expired: fetch fresh role and update cookie
  const role = await fetchUserRole(userId);
  console.debug('🐞 Role fetched:', role);

  cookieStore.set(
    ROLE_CACHE_NAME,
    JSON.stringify({
      role,
      userId,
      expiresAt: Date.now() + CACHE_DURATION,
      lastUpdated: Date.now(),
    }),
    {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: CACHE_DURATION / 1000,
    }
  );
  return role;
}

/**
 * Fetch user role from auth service (no cache)
 */
export async function fetchUserRole(userId: string): Promise<UserRole | null> {
  try {
    // Check admin role first (highest privilege)
    const adminResult = await auth.api.userHasPermission({
      body: {
        userId,
        permissions: {
          financial: ['create', 'read', 'update', 'delete'],
          organization: ['create', 'read', 'update'],
        },
      },
    });

    if (adminResult.success) {
      return 'admin';
    }

    // Check trainer role (medium privilege)
    const trainerResult = await auth.api.userHasPermission({
      body: {
        userId,
        permissions: {
          workout: ['create', 'read', 'update', 'delete'],
          customer: ['create', 'read', 'update', 'delete'],
        },
      },
    });

    if (trainerResult.success) {
      return 'trainer';
    }

    // Default to customer role
    return 'customer';
  } catch (error) {
    console.error('❌ Error fetching user role:', error);
    return null;
  }
}

// Role-based permission checking functions

/**
 * Check if user has admin role
 */
export async function isAdmin(userId: string): Promise<boolean> {
  const role = await getCachedUserRole(userId);
  return role === 'admin';
}

/**
 * Check if user has trainer role or higher
 */
export async function isTrainerOrAdmin(userId: string): Promise<boolean> {
  const role = await getCachedUserRole(userId);
  return role === 'admin' || role === 'trainer';
}

/**
 * Check if user can access financial features
 */
export async function canAccessFinancial(userId: string): Promise<boolean> {
  return await isAdmin(userId);
}

/**
 * Check if user can manage packages
 */
export async function canManagePackages(userId: string): Promise<boolean> {
  return await isAdmin(userId);
}

/**
 * Check if user can read packages
 */
export async function canReadPackages(userId: string): Promise<boolean> {
  return await isTrainerOrAdmin(userId);
}

/**
 * Check if user can manage organization
 */
export async function canManageOrganization(userId: string): Promise<boolean> {
  return await isAdmin(userId);
}

/**
 * Check if user can access organization
 */
export async function canAccessOrganization(userId: string): Promise<boolean> {
  return await isTrainerOrAdmin(userId);
}

/**
 * Check if user can manage customers
 */
export async function canManageCustomers(userId: string): Promise<boolean> {
  return await isTrainerOrAdmin(userId);
}

/**
 * Check if user can manage workouts
 */
export async function canManageWorkouts(userId: string): Promise<boolean> {
  return await isTrainerOrAdmin(userId);
}

// Legacy function aliases for backward compatibility
// These will be removed once all usages are updated

/**
 * @deprecated Use isAdmin instead
 */
export async function canAccessAdminCached(userId: string): Promise<boolean> {
  return await isAdmin(userId);
}

/**
 * @deprecated Use canAccessOrganization instead
 */
export async function canAccessOrganizationCached(userId: string): Promise<boolean> {
  return await canAccessOrganization(userId);
}

/**
 * @deprecated Use canManageCustomers instead
 */
export async function canAccessCustomerCached(userId: string): Promise<boolean> {
  return await canManageCustomers(userId);
}

/**
 * @deprecated Use specific permission functions instead
 */
export async function checkPermissionCached(userId: string, permission: string): Promise<boolean> {
  switch (permission) {
    case 'admin':
      return await isAdmin(userId);
    case 'financial':
      return await canAccessFinancial(userId);
    case 'package':
      return await canReadPackages(userId);
    case 'organization':
      return await canAccessOrganization(userId);
    case 'customer':
      return await canManageCustomers(userId);
    default:
      return false;
  }
}

/**
 * @deprecated Use getCachedUserRole instead
 * Legacy function for backward compatibility
 */
export async function getAllCommonPermissionsCached(userId: string): Promise<Record<string, boolean>> {
  const role = await getCachedUserRole(userId);

  // Convert role to legacy permission format
  const permissions: Record<string, boolean> = {
    admin: role === 'admin',
    trainer: role === 'admin' || role === 'trainer',
    customer: role !== null,
    financial: role === 'admin',
    package: role === 'admin' || role === 'trainer',
    organization: role === 'admin' || role === 'trainer',
  };

  return permissions;
}
