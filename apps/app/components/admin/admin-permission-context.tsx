'use client';
import { createContext, useContext } from 'react';

export type AdminPermission = {
  hasFinancialPermission: boolean;
  hasPackagePermission: boolean;
  hasOrganizationPermission: boolean;
};

const AdminPermissionContext = createContext<AdminPermission | null>(null);

export function useAdminPermission() {
  const ctx = useContext(AdminPermissionContext);
  if (!ctx) {
    throw new Error('useAdminPermission must be used within AdminPermissionProvider');
  }

  return ctx;
}

export function AdminPermissionProvider({ value, children }: { value: AdminPermission; children: React.ReactNode }) {
  return <AdminPermissionContext.Provider value={value}>{children}</AdminPermissionContext.Provider>;
}
