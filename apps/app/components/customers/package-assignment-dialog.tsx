'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { PackageSelector } from './package-selector';
import { assignPackageToCustomer } from '@/hooks/use-customer-packages';
import type { CustomerResponse, PackageResponse } from '@/lib/validations';
import { authClient } from '@workspace/auth';

interface PackageAssignmentDialogProps {
  customer: CustomerResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPackageAssigned?: () => void;
}

export function PackageAssignmentDialog({
  customer,
  open,
  onOpenChange,
  onPackageAssigned,
}: PackageAssignmentDialogProps) {
  const [selectedPackage, setSelectedPackage] = useState<PackageResponse | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasFinancialPermission, setHasFinancialPermission] = useState(false);

  // Check admin permissions for financial operations
  useEffect(() => {
    const checkPermissions = async () => {
      try {
        const hasPermission = await authClient.admin.hasPermission({
          permissions: {
            financial: ['create'],
          },
        });

        setHasFinancialPermission(hasPermission.data?.success ?? false);
      } catch (error) {
        console.error('Error checking permissions:', error);
        setHasFinancialPermission(false);
      }
    };

    if (open) {
      checkPermissions();
    }
  }, [open]);

  const handleAssignPackage = async () => {
    if (!customer || !selectedPackage) return;

    setIsSubmitting(true);
    try {
      await assignPackageToCustomer(customer.id, {
        packageId: selectedPackage.id,
      });

      toast.success(`Package "${selectedPackage.name}" assigned successfully`);
      onOpenChange(false);
      setSelectedPackage(null);

      if (onPackageAssigned) {
        onPackageAssigned();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to assign package';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen);
      if (!newOpen) {
        setSelectedPackage(null);
      }
    }
  };

  if (!customer) {
    return null;
  }

  // Don't render if user doesn't have financial permissions
  if (!hasFinancialPermission) {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Access Denied</DialogTitle>
            <DialogDescription>
              You don't have permission to assign packages. Only administrators can perform financial operations.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => handleOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Assign Package - {customer.name}</DialogTitle>
          <DialogDescription>
            Select a session package to assign to this customer. If the customer already has the same package type, the
            sessions will be combined.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <PackageSelector
            selectedPackageId={selectedPackage?.id}
            onPackageSelect={setSelectedPackage}
            disabled={isSubmitting}
          />
        </div>

        {selectedPackage && (
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Assignment Summary</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Package:</span>
                <span>{selectedPackage.name}</span>
              </div>
              <div className="flex justify-between">
                <span>Sessions:</span>
                <span>{selectedPackage.sessionCount}</span>
              </div>
              <div className="flex justify-between">
                <span>Price:</span>
                <span>RM {parseFloat(selectedPackage.price).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Per Session:</span>
                <span>RM {(parseFloat(selectedPackage.price) / selectedPackage.sessionCount).toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => handleOpenChange(false)} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleAssignPackage} disabled={!selectedPackage || isSubmitting}>
            {isSubmitting ? 'Assigning...' : 'Assign Package'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
