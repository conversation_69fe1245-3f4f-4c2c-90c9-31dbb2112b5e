'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import type { CustomerResponse } from '@/lib/validations';
import { PurchaseHistory } from './purchase-history';
import { CustomerPackagesList } from './customer-packages-list';
import { PackageAssignmentDialog } from './package-assignment-dialog';
import { useUserRole } from '@/hooks/use-user-role';

interface PurchaseDialogProps {
  customer: CustomerResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PurchaseDialog({ customer, open, onOpenChange }: PurchaseDialogProps) {
  const [isPackageDialogOpen, setIsPackageDialogOpen] = useState(false);
  const [hasFinancialPermission, setHasFinancialPermission] = useState(false);

  // Check admin permissions for financial operations
  useEffect(() => {
    const checkPermissions = async () => {
      try {
        const hasPermission = await authClient.admin.hasPermission({
          permissions: {
            financial: ['create'],
          },
        });

        setHasFinancialPermission(hasPermission.data?.success ?? false);
      } catch (error) {
        console.error('Error checking permissions:', error);
        setHasFinancialPermission(false);
      }
    };

    if (open) {
      checkPermissions();
    }
  }, [open]);

  if (!customer) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Manage Sessions - {customer.name}</DialogTitle>
          <DialogDescription>Assign session packages and view purchase history</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="packages" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="packages">Packages</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="packages" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Customer Packages</h3>
                <p className="text-sm text-muted-foreground">Manage session packages for this customer</p>
              </div>
              <Button onClick={() => setIsPackageDialogOpen(true)} disabled={!hasFinancialPermission}>
                Assign Package
              </Button>
              {!hasFinancialPermission && (
                <p className="text-sm text-muted-foreground">Admin permissions required for package assignment</p>
              )}
            </div>
            <CustomerPackagesList customerId={customer.id} />
          </TabsContent>

          <TabsContent value="history">
            <PurchaseHistory customerId={customer.id} />
          </TabsContent>
        </Tabs>
      </DialogContent>

      <PackageAssignmentDialog
        customer={customer}
        open={isPackageDialogOpen}
        onOpenChange={setIsPackageDialogOpen}
        onPackageAssigned={() => {
          // Refresh the customer packages list
          // This will be handled by SWR revalidation
        }}
      />
    </Dialog>
  );
}
