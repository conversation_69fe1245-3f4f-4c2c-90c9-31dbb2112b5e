'use client';

import { createContext, useContext, ReactNode } from 'react';
import { AuthUIProvider, authClient } from '@workspace/auth';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

const AuthContext = createContext(authClient);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const router = useRouter();

  return (
    <AuthUIProvider
      authClient={authClient}
      organization={{
        logo: {
          upload: async (file) => {
            // Your upload logic
            return 'https://picsum.photos/200';
          },
          size: 256,
          extension: 'png',
        },
        customRoles: [
          { role: 'trainer', label: 'Trainer' },
          { role: 'customer', label: 'Customer' },
        ],
      }}
      navigate={router.push}
      replace={router.replace}
      onSessionChange={() => {
        // Clear router cache (protected routes)
        router.refresh();
      }}
      Link={Link}
      nameRequired={true}
    >
      <AuthContext.Provider value={authClient}>{children}</AuthContext.Provider>
    </AuthUIProvider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
